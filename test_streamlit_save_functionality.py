#!/usr/bin/env python3
"""
Test script to verify the Streamlit save functionality works correctly
"""
import json
import tempfile
import pathlib
from datetime import datetime

# Import the save functions from streamlit_demo
import sys
sys.path.append('.')

def test_save_functions():
    """Test the quality results save functions"""
    
    # Create mock quality results
    mock_quality_results = {
        "test_image1.jpg_12345": {
            "image_path": "test_image1.jpg",
            "overall_assessment": {
                "score": 85.5,
                "level": "Good",
                "pass_fail": True,
                "issues_summary": ["Minor blur detected"],
                "recommendations": ["Use better lighting"]
            },
            "detailed_results": {
                "resolution": {"score": 90},
                "blur": {"score": 80},
                "damage": {"damage_score": 95}
            },
            "processing_time_seconds": 5.2
        },
        "test_image2.png_67890": {
            "image_path": "test_image2.png",
            "overall_assessment": {
                "score": 92.1,
                "level": "Excellent",
                "pass_fail": True,
                "issues_summary": [],
                "recommendations": ["Image quality is excellent"]
            },
            "detailed_results": {
                "resolution": {"score": 95},
                "blur": {"score": 90},
                "damage": {"damage_score": 90}
            },
            "processing_time_seconds": 4.8
        }
    }
    
    print("🧪 Testing Streamlit save functionality...")
    
    try:
        # Import the save functions
        from streamlit_demo import save_quality_results_to_files, create_quality_results_zip
        
        # Test 1: Save to local files
        print("\n📁 Test 1: Saving to local files...")
        with tempfile.TemporaryDirectory() as temp_dir:
            saved_files = save_quality_results_to_files(mock_quality_results, temp_dir)
            
            if saved_files:
                print(f"✅ Successfully saved {len(saved_files)} files:")
                for file_path in saved_files:
                    print(f"   • {file_path}")
                    
                    # Verify file content
                    with open(file_path, 'r') as f:
                        data = json.load(f)
                        print(f"   📊 Score: {data['overall_assessment']['score']}")
            else:
                print("❌ Failed to save files")
                return False
        
        # Test 2: Create ZIP file
        print("\n📦 Test 2: Creating ZIP file...")
        zip_data = create_quality_results_zip(mock_quality_results)
        
        if zip_data:
            print(f"✅ Successfully created ZIP file ({len(zip_data.getvalue())} bytes)")
            
            # Test ZIP content
            import zipfile
            import io
            
            zip_data.seek(0)
            with zipfile.ZipFile(zip_data, 'r') as zip_file:
                file_list = zip_file.namelist()
                print(f"   📄 ZIP contains {len(file_list)} files:")
                for filename in file_list:
                    print(f"      • {filename}")
        else:
            print("❌ Failed to create ZIP file")
            return False
        
        print("\n✅ All save functionality tests PASSED!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with exception: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Starting Streamlit save functionality test...")
    
    success = test_save_functions()
    
    if success:
        print("\n🎉 ***** Save functionality test PASSED!")
    else:
        print("\n💥 ***** Save functionality test FAILED!")
